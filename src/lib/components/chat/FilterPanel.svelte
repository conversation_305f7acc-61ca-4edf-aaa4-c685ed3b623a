<script lang="ts">
	import { createEventDispatcher, onMount, onDestroy } from 'svelte';
	import { Button, Input, Checkbox, Datepicker } from 'flowbite-svelte';
	import {
		CalendarMonthOutline,
		CloseOutline,
		SearchOutline,
		CheckCircleSolid,
		ArchiveSolid
	} from 'flowbite-svelte-icons';
	import { t } from '$lib/stores/i18n';

	export let isOpen = false;
	export let filterData = {
		dateRange: null,
		customStartDate: null,
		customEndDate: null,
		platforms: new Set(['All']),
		status: new Set(['All']),
		searchText: ''
	};

	const dispatch = createEventDispatcher();

	// Filter options
	const dateRangeOptions = [
		{ id: 'today', label: 'Today' },
		{ id: 'week', label: 'This Week' },
		{ id: 'month', label: 'This Month' },
		{ id: 'custom', label: 'Custom Range' }
	];

	const platformOptions = [
		{ id: 'All', label: 'All Platforms', color: 'bg-gray-100 text-gray-700' },
		{ id: 'LINE', label: 'LINE', color: 'bg-green-100 text-green-700' },
		{ id: 'WHATSAPP', label: 'WhatsApp', color: 'bg-green-100 text-green-700' },
		{ id: 'FACEBOOK', label: 'Facebook', color: 'bg-blue-100 text-blue-700' },
		{ id: 'INSTAGRAM', label: 'Instagram', color: 'bg-purple-100 text-purple-700' },
		{ id: 'TELEGRAM', label: 'Telegram', color: 'bg-sky-100 text-sky-700' }
	];

	const statusOptions = [
		{ id: 'All', label: 'All Status', icon: null },
		{ id: 'Active', label: 'Active', icon: CheckCircleSolid },
		{ id: 'Archived', label: 'Archived', icon: ArchiveSolid }
	];

	let filterPanelElement: HTMLElement;
	let searchInput: HTMLInputElement;

	// Local state for form inputs
	let selectedDateRange = filterData.dateRange;
	let customStartDate = filterData.customStartDate;
	let customEndDate = filterData.customEndDate;
	let selectedPlatforms = new Set(filterData.platforms);
	let selectedStatus = new Set(filterData.status);
	let searchText = filterData.searchText;

	$: showCustomDateInputs = selectedDateRange === 'custom';

	onMount(() => {
		if (isOpen) {
			// Focus the search input when panel opens
			setTimeout(() => {
				searchInput?.focus();
			}, 100);
		}

		// Handle click outside to close
		const handleClickOutside = (event: MouseEvent) => {
			if (isOpen && filterPanelElement && !filterPanelElement.contains(event.target as Node)) {
				closePanel();
			}
		};

		// Handle escape key to close
		const handleKeydown = (event: KeyboardEvent) => {
			if (event.key === 'Escape' && isOpen) {
				closePanel();
			}
		};

		document.addEventListener('click', handleClickOutside);
		document.addEventListener('keydown', handleKeydown);

		return () => {
			document.removeEventListener('click', handleClickOutside);
			document.removeEventListener('keydown', handleKeydown);
		};
	});

	function closePanel() {
		isOpen = false;
		dispatch('close');
	}

	function clearAllFilters() {
		selectedDateRange = null;
		customStartDate = null;
		customEndDate = null;
		selectedPlatforms = new Set(['All']);
		selectedStatus = new Set(['All']);
		searchText = '';
		applyFilters();
	}

	function applyFilters() {
		const updatedFilterData = {
			dateRange: selectedDateRange,
			customStartDate,
			customEndDate,
			platforms: selectedPlatforms,
			status: selectedStatus,
			searchText
		};

		dispatch('apply', updatedFilterData);
	}

	function togglePlatform(platformId: string) {
		if (platformId === 'All') {
			selectedPlatforms = new Set(['All']);
		} else {
			selectedPlatforms.delete('All');
			if (selectedPlatforms.has(platformId)) {
				selectedPlatforms.delete(platformId);
			} else {
				selectedPlatforms.add(platformId);
			}

			// If no platforms selected, select All
			if (selectedPlatforms.size === 0) {
				selectedPlatforms.add('All');
			}
		}
		selectedPlatforms = selectedPlatforms; // Trigger reactivity
	}

	function toggleStatus(statusId: string) {
		if (statusId === 'All') {
			selectedStatus = new Set(['All']);
		} else {
			selectedStatus.delete('All');
			if (selectedStatus.has(statusId)) {
				selectedStatus.delete(statusId);
			} else {
				selectedStatus.add(statusId);
			}

			// If no status selected, select All
			if (selectedStatus.size === 0) {
				selectedStatus.add('All');
			}
		}
		selectedStatus = selectedStatus; // Trigger reactivity
	}

	// Count active filters
	$: activeFiltersCount = [
		selectedDateRange ? 1 : 0,
		selectedPlatforms.size > 1 || !selectedPlatforms.has('All') ? 1 : 0,
		selectedStatus.size > 1 || !selectedStatus.has('All') ? 1 : 0,
		searchText ? 1 : 0
	].reduce((sum, count) => sum + count, 0);
</script>

{#if isOpen}
	<div
		bind:this={filterPanelElement}
		class="absolute right-0 top-full z-50 mt-2 w-80 rounded-lg border border-gray-200 bg-white p-4 shadow-lg"
		role="dialog"
		aria-label="Filter conversations"
		aria-modal="true"
	>
		<!-- Header -->
		<div class="mb-4 flex items-center justify-between">
			<h3 class="text-lg font-semibold text-gray-900">{t('filter')}</h3>
			<div class="flex items-center gap-2">
				{#if activeFiltersCount > 0}
					<span class="text-sm text-gray-500">
						{activeFiltersCount} active
					</span>
				{/if}
				<button
					type="button"
					on:click={closePanel}
					class="rounded-lg p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600"
					aria-label="Close filter panel"
				>
					<CloseOutline class="h-5 w-5" />
				</button>
			</div>
		</div>

		<!-- Search Filter -->
		<div class="mb-4">
			<label for="filter-search" class="mb-2 block text-sm font-medium text-gray-700">
				Search Conversations
			</label>
			<div class="relative">
				<SearchOutline class="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
				<Input
					id="filter-search"
					bind:this={searchInput}
					bind:value={searchText}
					placeholder="Search by name, platform, message..."
					class="pl-10"
					on:input={applyFilters}
				/>
			</div>
		</div>

		<!-- Date Range Filter -->
		<div class="mb-4">
			<label class="mb-2 block text-sm font-medium text-gray-700">
				<CalendarMonthOutline class="mr-1 inline h-4 w-4" />
				Date Range
			</label>
			<div class="space-y-2">
				{#each dateRangeOptions as option}
					<label class="flex items-center">
						<input
							type="radio"
							bind:group={selectedDateRange}
							value={option.id}
							on:change={applyFilters}
							class="mr-2 text-blue-600 focus:ring-blue-500"
						/>
						<span class="text-sm text-gray-700">{option.label}</span>
					</label>
				{/each}
			</div>

			{#if showCustomDateInputs}
				<div class="mt-3 space-y-2">
					<div>
						<label for="start-date" class="block text-xs text-gray-600">Start Date</label>
						<Datepicker
							id="start-date"
							bind:value={customStartDate}
							placeholder="Select start date"
							class="text-sm"
							on:change={applyFilters}
						/>
					</div>
					<div>
						<label for="end-date" class="block text-xs text-gray-600">End Date</label>
						<Datepicker
							id="end-date"
							bind:value={customEndDate}
							placeholder="Select end date"
							class="text-sm"
							on:change={applyFilters}
						/>
					</div>
				</div>
			{/if}
		</div>

		<!-- Platform Filter -->
		<div class="mb-4">
			<label class="mb-2 block text-sm font-medium text-gray-700">Platform</label>
			<div class="max-h-32 space-y-1 overflow-y-auto">
				{#each platformOptions as platform}
					<label class="flex items-center rounded p-2 hover:bg-gray-50">
						<Checkbox
							checked={selectedPlatforms.has(platform.id)}
							on:change={() => {
								togglePlatform(platform.id);
								applyFilters();
							}}
							class="mr-2"
						/>
						<span class="flex-1 text-sm text-gray-700">{platform.label}</span>
						{#if platform.id !== 'All'}
							<span class="rounded-full px-2 py-0.5 text-xs {platform.color}">
								{platform.id}
							</span>
						{/if}
					</label>
				{/each}
			</div>
		</div>

		<!-- Status Filter -->
		<div class="mb-4">
			<label class="mb-2 block text-sm font-medium text-gray-700">Status</label>
			<div class="space-y-1">
				{#each statusOptions as status}
					<label class="flex items-center rounded p-2 hover:bg-gray-50">
						<Checkbox
							checked={selectedStatus.has(status.id)}
							on:change={() => {
								toggleStatus(status.id);
								applyFilters();
							}}
							class="mr-2"
						/>
						<span class="flex items-center text-sm text-gray-700">
							{#if status.icon}
								<svelte:component this={status.icon} class="mr-1 h-4 w-4" />
							{/if}
							{status.label}
						</span>
					</label>
				{/each}
			</div>
		</div>

		<!-- Actions -->
		<div class="flex justify-between border-t border-gray-200 pt-4">
			<Button
				color="none"
				class="text-gray-600 hover:bg-gray-100"
				on:click={clearAllFilters}
				disabled={activeFiltersCount === 0}
			>
				{t('clear')}
			</Button>
			<Button color="blue" on:click={closePanel}>Done</Button>
		</div>
	</div>
{/if}
